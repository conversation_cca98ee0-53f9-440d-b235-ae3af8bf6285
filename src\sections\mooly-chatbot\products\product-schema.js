import { z as zod } from 'zod';

import { PRODUCT_TYPES } from 'src/actions/mooly-chatbot/product-constants';

import { schemaHelper } from 'src/components/hook-form';

// ----------------------------------------------------------------------

// Helper function để tạo schema động dựa trên loại sản phẩm
const createDynamicSchema = (productType) => {
  const isSimple = productType === PRODUCT_TYPES.SIMPLE;

  return zod.object({
    // Thông tin cơ bản - bắt buộc cho tất cả
    name: zod.string().min(1, 'Tên sản phẩm là bắt buộc'),
    slug: zod.string().optional(),
    description: schemaHelper.editor().optional(),
    shortDescription: zod.string().optional(),

    // Phân loại - bắt buộc
    categoryId: zod.string().min(1, '<PERSON>h mục là bắt buộc'),
    type: zod.enum([
      PRODUCT_TYPES.SIMPLE,
      PRODUCT_TYPES.VARIABLE,
      PRODUCT_TYPES.DIGITAL,
      PRODUCT_TYPES.SERVICE,
    ]).default(PRODUCT_TYPES.SIMPLE),

    // Mã sản phẩm - bắt buộc
    sku: zod.string().min(1, 'Mã SKU là bắt buộc'),

    // Giá và tồn kho - bắt buộc
    price: zod.number({
      required_error: 'Giá sản phẩm là bắt buộc',
      invalid_type_error: 'Giá phải là số'
    }).min(0, 'Giá phải lớn hơn hoặc bằng 0'),

    stockQuantity: zod.number({
      required_error: 'Số lượng tồn kho là bắt buộc',
      invalid_type_error: 'Số lượng phải là số'
    }).min(0, 'Số lượng tồn kho phải lớn hơn hoặc bằng 0'),

    trackInventory: zod.boolean().default(true),

    // Hình ảnh - bắt buộc
    images: zod.array(zod.any()).min(1, 'Ít nhất một hình ảnh sản phẩm là bắt buộc'),
    avatar: zod.any().optional(),

    // Trạng thái
    isActive: zod.boolean().default(true),

    // Các trường tùy chọn cho sản phẩm nâng cao
    ...(isSimple ? {} : {
      costPrice: zod.number().min(0, 'Giá vốn phải lớn hơn hoặc bằng 0').optional().nullable(),
      salePrice: zod.number().min(0, 'Giá khuyến mãi phải lớn hơn hoặc bằng 0').optional().nullable(),

      // Kích thước và trọng lượng
      weight: zod.number().min(0, 'Trọng lượng phải lớn hơn hoặc bằng 0').optional().nullable(),
      length: zod.number().min(0, 'Chiều dài phải lớn hơn hoặc bằng 0').optional().nullable(),
      width: zod.number().min(0, 'Chiều rộng phải lớn hơn hoặc bằng 0').optional().nullable(),
      height: zod.number().min(0, 'Chiều cao phải lớn hơn hoặc bằng 0').optional().nullable(),

      // Biến thể và thuộc tính
      attributes: zod.array(zod.object({
        name: zod.string(),
        values: zod.array(zod.string())
      })).default([]),
      variants: zod.any().default([]),
      hasVariants: zod.boolean().default(false),

      // Tags và labels
      tags: zod.array(zod.string()).default([]),
      gender: zod.array(zod.string()).default([]),

      // Labels sản phẩm
      saleLabel: zod.object({
        enabled: zod.boolean().default(false),
        content: zod.string().default(''),
      }).default({ enabled: false, content: '' }),

      newLabel: zod.object({
        enabled: zod.boolean().default(false),
        content: zod.string().default(''),
      }).default({ enabled: false, content: '' }),

      // Thuế và pricing
      taxes: zod.number().min(0, 'Thuế phải lớn hơn hoặc bằng 0').optional().nullable(),
      includeTaxes: zod.boolean().default(false),

      // SEO
      metaKeywords: zod.array(zod.string()).default([]),
      seoTitle: zod.string().optional(),
      seoDescription: zod.string().optional(),

      // Trạng thái nâng cao
      isFeatured: zod.boolean().default(false),

      // Thông tin bổ sung
      marketingInfo: zod.any().default({}),
      inventorySettings: zod.any().default({}),
      pricingSettings: zod.any().default({}),
      digitalProductInfo: zod.any().default({}),
      serviceInfo: zod.any().default({}),
      bundleInfo: zod.any().default({}),
      dimensions: zod.any().default({}),
    })
  });
};

// ----------------------------------------------------------------------

// Schema chính - sử dụng schema đầy đủ để tương thích
export const ProductSchema = createDynamicSchema(PRODUCT_TYPES.VARIABLE);

// Schema đơn giản cho sản phẩm simple
export const SimpleProductSchema = createDynamicSchema(PRODUCT_TYPES.SIMPLE);

// Function để tạo schema dựa trên loại sản phẩm
export const getSchemaByType = (productType) => createDynamicSchema(productType);

// ----------------------------------------------------------------------

// Default values cho sản phẩm simple
export const simpleProductDefaultValues = {
  // Thông tin cơ bản
  name: '',
  slug: '',
  description: '',
  shortDescription: '',

  // Phân loại
  categoryId: '',
  type: PRODUCT_TYPES.SIMPLE,

  // Mã sản phẩm
  sku: '',

  // Giá và tồn kho
  price: null,
  stockQuantity: 0,
  trackInventory: true,

  // Hình ảnh và media
  images: [],
  avatar: null,

  // Trạng thái
  isActive: true,
};

// Default values đầy đủ cho tất cả loại sản phẩm
export const defaultValues = {
  // Thông tin cơ bản
  name: '',
  slug: '',
  description: '',
  shortDescription: '',

  // Phân loại
  categoryId: '',
  type: PRODUCT_TYPES.SIMPLE, // Mặc định là simple để đơn giản hóa

  // Mã sản phẩm
  sku: '',

  // Giá và tồn kho
  price: null,
  costPrice: null,
  salePrice: null,
  stockQuantity: 0,
  trackInventory: true,

  // Kích thước và trọng lượng
  weight: null,
  length: null,
  width: null,
  height: null,

  // Hình ảnh và media
  images: [],
  avatar: null,

  // Biến thể và thuộc tính
  attributes: [],
  variants: [],

  // Tags và labels
  tags: [],
  gender: [],

  // Labels sản phẩm
  saleLabel: { enabled: false, content: '' },
  newLabel: { enabled: false, content: '' },

  // Thuế và pricing
  taxes: null,
  includeTaxes: false,

  // SEO
  metaKeywords: [],
  seoTitle: '',
  seoDescription: '',

  // Trạng thái
  isActive: true,
  isFeatured: false,

  // Thông tin bổ sung
  marketingInfo: {},
  inventorySettings: {},
  pricingSettings: {},
  digitalProductInfo: {},
  serviceInfo: {},
  bundleInfo: {},
  dimensions: {},
};
